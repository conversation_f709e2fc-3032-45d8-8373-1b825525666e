import React, { useState } from 'react';
import { useAuth } from '../../contexts/AuthContext';
import PasswordManager from '../auth/PasswordManager';
import SecurityAudit from '../auth/SecurityAudit';

const SettingsManagement: React.FC = () => {
  const { admin } = useAuth();
  const [showPasswordManager, setShowPasswordManager] = useState(false);
  const [showSecurityAudit, setShowSecurityAudit] = useState(false);
  const [settings, setSettings] = useState({
    siteName: 'منصة علاء عبد الحميد التعليمية',
    adminEmail: '<EMAIL>',
    maxStudentsPerCourse: 100,
    certificateAutoIssue: true,
    emailNotifications: true,
    maintenanceMode: false
  });

  const [showSuccessMessage, setShowSuccessMessage] = useState<string | null>(null);

  const handleSaveSettings = () => {
    // TODO: Save settings to database
    console.log('Settings saved:', settings);
    setShowSuccessMessage('تم حفظ الإعدادات بنجاح!');
    setTimeout(() => setShowSuccessMessage(null), 3000);
  };

  const handlePasswordChange = () => {
    setShowPasswordManager(true);
  };

  const handleSecurityAudit = () => {
    setShowSecurityAudit(true);
  };

  const handleBackup = () => {
    setShowSuccessMessage('سيتم إضافة وظيفة النسخ الاحتياطي قريباً');
    setTimeout(() => setShowSuccessMessage(null), 3000);
  };

  return (
    <div className="space-y-6">
      {/* Success Message */}
      {showSuccessMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg relative">
          <div className="flex items-center">
            <i className="fas fa-check-circle ml-2"></i>
            <span>{showSuccessMessage}</span>
          </div>
          <button
            onClick={() => setShowSuccessMessage(null)}
            className="absolute top-0 bottom-0 left-0 px-4 py-3"
          >
            <i className="fas fa-times"></i>
          </button>
        </div>
      )}

      {/* Header */}
      <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
              إعدادات النظام
            </h1>
            <p className="text-gray-600 mt-2">إدارة إعدادات المنصة والأمان والتخصيص</p>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={handleSaveSettings}
              className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-200"
            >
              <i className="fas fa-save ml-2"></i>
              حفظ الإعدادات
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* General Settings */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">الإعدادات العامة</h3>
          </div>
          <div className="p-6 space-y-4">
            <div>
              <label className="form-label">اسم المنصة</label>
              <input
                type="text"
                value={settings.siteName}
                onChange={(e) => setSettings({...settings, siteName: e.target.value})}
                className="form-input"
              />
            </div>
            
            <div>
              <label className="form-label">بريد المدير الإلكتروني</label>
              <input
                type="email"
                value={settings.adminEmail}
                onChange={(e) => setSettings({...settings, adminEmail: e.target.value})}
                className="form-input"
              />
            </div>
            
            <div>
              <label className="form-label">الحد الأقصى للطلاب في الدورة</label>
              <input
                type="number"
                value={settings.maxStudentsPerCourse}
                onChange={(e) => setSettings({...settings, maxStudentsPerCourse: parseInt(e.target.value)})}
                className="form-input"
              />
            </div>
          </div>
        </div>

        {/* Feature Settings */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">إعدادات المميزات</h3>
          </div>
          <div className="p-6 space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <label className="font-medium text-gray-900">إصدار الشهادات تلقائياً</label>
                <p className="text-sm text-gray-500">إصدار شهادة عند إكمال الدورة</p>
              </div>
              <input
                type="checkbox"
                checked={settings.certificateAutoIssue}
                onChange={(e) => setSettings({...settings, certificateAutoIssue: e.target.checked})}
                className="w-4 h-4"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="font-medium text-gray-900">إشعارات البريد الإلكتروني</label>
                <p className="text-sm text-gray-500">إرسال إشعارات للطلاب والمديرين</p>
              </div>
              <input
                type="checkbox"
                checked={settings.emailNotifications}
                onChange={(e) => setSettings({...settings, emailNotifications: e.target.checked})}
                className="w-4 h-4"
              />
            </div>
            
            <div className="flex items-center justify-between">
              <div>
                <label className="font-medium text-gray-900">وضع الصيانة</label>
                <p className="text-sm text-gray-500">إغلاق المنصة مؤقتاً للصيانة</p>
              </div>
              <input
                type="checkbox"
                checked={settings.maintenanceMode}
                onChange={(e) => setSettings({...settings, maintenanceMode: e.target.checked})}
                className="w-4 h-4"
              />
            </div>
          </div>
        </div>

        {/* Security Settings */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">إعدادات الأمان</h3>
          </div>
          <div className="p-6 space-y-4">
            <button
              onClick={handlePasswordChange}
              className="w-full bg-blue-50 hover:bg-blue-100 text-blue-700 font-semibold py-3 px-4 rounded-lg transition-colors border border-blue-200"
            >
              <i className="fas fa-key ml-2"></i>
              تغيير كلمة مرور المدير
            </button>

            <button
              onClick={handleSecurityAudit}
              className="w-full bg-green-50 hover:bg-green-100 text-green-700 font-semibold py-3 px-4 rounded-lg transition-colors border border-green-200"
            >
              <i className="fas fa-shield-alt ml-2"></i>
              تدقيق الأمان
            </button>

            <button
              onClick={handleBackup}
              className="w-full bg-purple-50 hover:bg-purple-100 text-purple-700 font-semibold py-3 px-4 rounded-lg transition-colors border border-purple-200"
            >
              <i className="fas fa-download ml-2"></i>
              تصدير نسخة احتياطية
            </button>
          </div>
        </div>

        {/* System Info */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">معلومات النظام</h3>
          </div>
          <div className="p-6 space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-600">إصدار المنصة:</span>
              <span className="font-medium">1.0.0</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">آخر تحديث:</span>
              <span className="font-medium">2024-07-14</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">حالة قاعدة البيانات:</span>
              <span className="text-green-600 font-medium">متصلة</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">مساحة التخزين:</span>
              <span className="font-medium">2.5 GB / 10 GB</span>
            </div>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button
          onClick={handleSaveSettings}
          className="btn btn-primary"
        >
          <i className="fas fa-save"></i>
          حفظ الإعدادات
        </button>
      </div>

      {/* Password Manager Modal */}
      <PasswordManager
        isOpen={showPasswordManager}
        onClose={() => setShowPasswordManager(false)}
        userEmail={admin?.email}
      />

      {/* Security Audit Modal */}
      <SecurityAudit
        isOpen={showSecurityAudit}
        onClose={() => setShowSecurityAudit(false)}
      />
    </div>
  );
};

export default SettingsManagement;
