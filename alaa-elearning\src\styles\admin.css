/* Admin Panel Styles - Blue & White Theme */

/* Admin Login Page */
.admin-login-container {
  background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #1e40af 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.admin-login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  max-width: 450px;
  width: 100%;
}

.admin-login-header {
  text-align: center;
  margin-bottom: 30px;
}

.admin-login-icon {
  width: 80px;
  height: 80px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.admin-login-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #1e40af;
  margin-bottom: 10px;
}

.admin-login-subtitle {
  color: #64748b;
  font-size: 1.1rem;
}

/* Admin Dashboard */
.admin-dashboard {
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  min-height: 100vh;
  display: flex;
  direction: rtl;
}

.admin-sidebar {
  background: linear-gradient(180deg, #1e3a8a 0%, #1e40af 50%, #1d4ed8 100%);
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
  position: fixed;
  right: 0;
  top: 0;
  height: 100vh;
  z-index: 50;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
}

.admin-sidebar-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.admin-sidebar-logo {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.admin-sidebar-nav {
  padding: 20px 15px;
  flex: 1;
  overflow-y: auto;
}

.admin-nav-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  border-radius: 15px;
  margin-bottom: 8px;
  transition: all 0.3s ease;
  color: #bfdbfe;
  text-decoration: none;
  position: relative;
}

.admin-nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: translateX(-3px);
}

.admin-nav-item.active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border-left: 4px solid #fbbf24;
}

.admin-nav-item.active::before {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 30px;
  background: white;
  border-radius: 2px 0 0 2px;
}

/* Admin Header */
.admin-header {
  background: white;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-bottom: 2px solid #e2e8f0;
  padding: 20px 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.admin-header-title {
  font-size: 2rem;
  font-weight: bold;
  background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.admin-header-profile {
  display: flex;
  align-items: center;
  gap: 15px;
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  color: white;
  padding: 12px 20px;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.admin-header-avatar {
  width: 40px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Stats Cards */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
  margin-bottom: 30px;
}

.stats-card {
  background: white;
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #1e40af);
}

.stats-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12);
}

.stats-card-icon {
  width: 60px;
  height: 60px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  color: white;
  font-size: 1.5rem;
}

.stats-card-value {
  font-size: 2.5rem;
  font-weight: bold;
  color: #1e293b;
  margin-bottom: 10px;
}

.stats-card-label {
  color: #64748b;
  font-weight: 600;
  margin-bottom: 15px;
}

.stats-card-change {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
}

.stats-card-change.positive {
  background: #dcfce7;
  color: #166534;
}

.stats-card-change.negative {
  background: #fef2f2;
  color: #dc2626;
}

/* Quick Actions */
.quick-actions {
  background: white;
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid #e2e8f0;
}

.quick-actions-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: #1e293b;
  margin-bottom: 25px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.quick-action-btn {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 2px solid #e2e8f0;
  border-radius: 15px;
  padding: 20px;
  text-align: right;
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
  display: block;
}

.quick-action-btn:hover {
  transform: scale(1.02);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.quick-action-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  color: white;
  font-size: 1.2rem;
}

.quick-action-title {
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 5px;
}

.quick-action-desc {
  color: #64748b;
  font-size: 0.9rem;
}

/* Form Styles */
.admin-form-group {
  margin-bottom: 20px;
}

.admin-form-label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
  font-size: 0.95rem;
}

.admin-form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f9fafb;
}

.admin-form-input:focus {
  outline: none;
  border-color: #3b82f6;
  background: white;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.admin-btn {
  padding: 12px 24px;
  border-radius: 10px;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  font-size: 1rem;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.admin-btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  color: white;
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.3);
}

.admin-btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(59, 130, 246, 0.4);
}

.admin-btn-secondary {
  background: #f1f5f9;
  color: #475569;
  border: 2px solid #e2e8f0;
}

.admin-btn-secondary:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
}

/* Modal Styles */
.admin-modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  padding: 20px;
}

.admin-modal {
  background: white;
  border-radius: 20px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
}

.admin-modal-header {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  color: white;
  padding: 25px 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.admin-modal-title {
  font-size: 1.25rem;
  font-weight: bold;
}

.admin-modal-close {
  width: 35px;
  height: 35px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.admin-modal-close:hover {
  background: rgba(255, 255, 255, 0.3);
}

.admin-modal-body {
  padding: 30px;
}

.admin-modal-footer {
  background: #f8fafc;
  padding: 20px 30px;
  display: flex;
  gap: 15px;
  justify-content: flex-end;
}

/* Responsive */
@media (max-width: 768px) {
  .admin-sidebar {
    transform: translateX(100%);
  }
  
  .admin-sidebar.open {
    transform: translateX(0);
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
}
