import React, { useState } from 'react';

const CertificateManagement: React.FC = () => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState<string | null>(null);

  const handleCreateTemplate = () => {
    setShowCreateModal(true);
  };

  const handleTemplateCreated = () => {
    setShowCreateModal(false);
    setShowSuccessMessage('تم إنشاء قالب الشهادة بنجاح!');
    setTimeout(() => setShowSuccessMessage(null), 3000);
  };

  const handleIssueCertificate = () => {
    setShowSuccessMessage('سيتم إضافة وظيفة إصدار الشهادات قريباً');
    setTimeout(() => setShowSuccessMessage(null), 3000);
  };

  const handleViewHistory = () => {
    setShowSuccessMessage('سيتم إضافة سجل الشهادات قريباً');
    setTimeout(() => setShowSuccessMessage(null), 3000);
  };

  return (
    <div className="space-y-6">
      {/* Success Message */}
      {showSuccessMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg relative">
          <div className="flex items-center">
            <i className="fas fa-check-circle ml-2"></i>
            <span>{showSuccessMessage}</span>
          </div>
          <button
            onClick={() => setShowSuccessMessage(null)}
            className="absolute top-0 bottom-0 left-0 px-4 py-3"
          >
            <i className="fas fa-times"></i>
          </button>
        </div>
      )}

      {/* Header */}
      <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
              إدارة الشهادات
            </h1>
            <p className="text-gray-600 mt-2">إنشاء قوالب الشهادات وإصدارها للطلاب المتميزين</p>
          </div>
          <button
            onClick={handleCreateTemplate}
            className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg"
          >
            <i className="fas fa-plus ml-2"></i>
            إضافة قالب جديد
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl shadow-lg p-6 border border-yellow-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-yellow-700">قوالب الشهادات</p>
              <p className="text-3xl font-bold text-yellow-900">3</p>
              <p className="text-xs text-yellow-600 mt-1">قوالب متاحة</p>
            </div>
            <div className="w-14 h-14 bg-yellow-500 rounded-xl flex items-center justify-center">
              <i className="fas fa-certificate text-white text-xl"></i>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl shadow-lg p-6 border border-purple-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-purple-700">الشهادات المصدرة</p>
              <p className="text-3xl font-bold text-purple-900">89</p>
              <p className="text-xs text-purple-600 mt-1">إجمالي الشهادات</p>
            </div>
            <div className="w-14 h-14 bg-purple-500 rounded-xl flex items-center justify-center">
              <i className="fas fa-award text-white text-xl"></i>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl shadow-lg p-6 border border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-green-700">هذا الشهر</p>
              <p className="text-3xl font-bold text-green-900">12</p>
              <p className="text-xs text-green-600 mt-1">شهادات جديدة</p>
            </div>
            <div className="w-14 h-14 bg-green-500 rounded-xl flex items-center justify-center">
              <i className="fas fa-calendar text-white text-xl"></i>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl shadow-lg p-6 border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-blue-700">معدل النجاح</p>
              <p className="text-3xl font-bold text-blue-900">92%</p>
              <p className="text-xs text-blue-600 mt-1">نسبة الحصول على الشهادة</p>
            </div>
            <div className="w-14 h-14 bg-blue-500 rounded-xl flex items-center justify-center">
              <i className="fas fa-chart-line text-white text-xl"></i>
            </div>
          </div>
        </div>
      </div>

      {/* Action Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-lg p-8 border border-gray-100 hover:shadow-xl transition-shadow duration-300">
          <div className="text-center">
            <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="fas fa-certificate text-yellow-600 text-2xl"></i>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">قوالب الشهادات</h3>
            <p className="text-gray-600 mb-4">إدارة قوالب الشهادات المختلفة</p>
            <button
              onClick={handleCreateTemplate}
              className="bg-yellow-600 hover:bg-yellow-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
            >
              إدارة القوالب
            </button>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-8 border border-gray-100 hover:shadow-xl transition-shadow duration-300">
          <div className="text-center">
            <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="fas fa-award text-purple-600 text-2xl"></i>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">إصدار شهادات</h3>
            <p className="text-gray-600 mb-4">إصدار شهادات للطلاب المتميزين</p>
            <button
              onClick={handleIssueCertificate}
              className="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
            >
              إصدار شهادة
            </button>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg p-8 border border-gray-100 hover:shadow-xl transition-shadow duration-300">
          <div className="text-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="fas fa-history text-green-600 text-2xl"></i>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">سجل الشهادات</h3>
            <p className="text-gray-600 mb-4">عرض جميع الشهادات المصدرة</p>
            <button
              onClick={handleViewHistory}
              className="bg-green-600 hover:bg-green-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
            >
              عرض السجل
            </button>
          </div>
        </div>
      </div>

      {/* Development Notice */}
      <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-8 text-center">
        <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <i className="fas fa-tools text-blue-600 text-2xl"></i>
        </div>
        <h3 className="text-lg font-semibold text-gray-700 mb-2">قيد التطوير</h3>
        <p className="text-gray-500 mb-4">نظام إدارة الشهادات المتقدم سيكون متاحاً قريباً</p>
        <div className="flex items-center justify-center gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-2">
            <i className="fas fa-check text-green-500"></i>
            <span>تصميم القوالب</span>
          </div>
          <div className="flex items-center gap-2">
            <i className="fas fa-check text-green-500"></i>
            <span>إصدار تلقائي</span>
          </div>
          <div className="flex items-center gap-2">
            <i className="fas fa-check text-green-500"></i>
            <span>تحقق رقمي</span>
          </div>
        </div>
      </div>

      {/* Create Template Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-lg">
            <div className="bg-gradient-to-r from-yellow-600 to-yellow-700 text-white p-6 rounded-t-2xl">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-bold">إنشاء قالب شهادة</h3>
                  <p className="text-yellow-100 text-sm mt-1">تصميم قالب شهادة جديد</p>
                </div>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-all duration-200"
                >
                  <i className="fas fa-times text-white"></i>
                </button>
              </div>
            </div>
            <div className="p-6 text-center">
              <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="fas fa-certificate text-yellow-600 text-xl"></i>
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">قريباً...</h4>
              <p className="text-gray-600 mb-6">سيتم إضافة منشئ قوالب الشهادات قريباً</p>
              <button
                onClick={handleTemplateCreated}
                className="bg-yellow-600 hover:bg-yellow-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
              >
                موافق
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CertificateManagement;
