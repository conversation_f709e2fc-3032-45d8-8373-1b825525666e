import React, { useState, useEffect } from 'react';
import { AccessCode, Course } from '../../types';
import LoadingSpinner from '../LoadingSpinner';

const AccessCodeManagement: React.FC = () => {
  const [accessCodes, setAccessCodes] = useState<AccessCode[]>([]);
  const [courses, setCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState<string | null>(null);
  const [newCodeData, setNewCodeData] = useState({
    courseIds: [] as string[],
    quantity: 1,
    expiresAt: ''
  });

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      // TODO: Replace with actual API calls
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockCourses: Course[] = [
        {
          id: '1',
          title: 'أساسيات التسويق متعدد المستويات',
          description: '',
          level: 1,
          sections: [],
          isActive: true,
          createdBy: 'admin',
          createdAt: new Date(),
          updatedAt: new Date()
        },
        {
          id: '2',
          title: 'استراتيجيات التسويق المتقدمة',
          description: '',
          level: 2,
          sections: [],
          isActive: true,
          createdBy: 'admin',
          createdAt: new Date(),
          updatedAt: new Date()
        }
      ];

      const mockAccessCodes: AccessCode[] = [
        {
          id: '1',
          code: '1234567',
          userId: 'user1',
          courseIds: ['1'],
          isUsed: true,
          createdBy: 'admin',
          createdAt: new Date('2024-01-15'),
          usedAt: new Date('2024-01-16')
        },
        {
          id: '2',
          code: '2345678',
          courseIds: ['1', '2'],
          isUsed: false,
          createdBy: 'admin',
          createdAt: new Date('2024-02-01')
        },
        {
          id: '3',
          code: '3456789',
          courseIds: ['2'],
          isUsed: false,
          expiresAt: new Date('2024-12-31'),
          createdBy: 'admin',
          createdAt: new Date('2024-02-10')
        }
      ];
      
      setCourses(mockCourses);
      setAccessCodes(mockAccessCodes);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const generateAccessCodes = async () => {
    try {
      if (newCodeData.courseIds.length === 0) {
        alert('يرجى اختيار دورة واحدة على الأقل');
        return;
      }

      const newCodes: AccessCode[] = [];

      for (let i = 0; i < newCodeData.quantity; i++) {
        const code = Math.floor(1000000 + Math.random() * 9000000).toString();
        newCodes.push({
          id: Date.now().toString() + i,
          code,
          courseIds: newCodeData.courseIds,
          isUsed: false,
          expiresAt: newCodeData.expiresAt ? new Date(newCodeData.expiresAt) : undefined,
          createdBy: 'admin',
          createdAt: new Date()
        });
      }

      setAccessCodes([...accessCodes, ...newCodes]);
      setNewCodeData({ courseIds: [], quantity: 1, expiresAt: '' });
      setShowCreateModal(false);
      setShowSuccessMessage(`تم إنشاء ${newCodeData.quantity} كود وصول بنجاح!`);

      // Hide success message after 3 seconds
      setTimeout(() => setShowSuccessMessage(null), 3000);
    } catch (error) {
      console.error('Error generating access codes:', error);
      alert('حدث خطأ أثناء إنشاء أكواد الوصول');
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setShowSuccessMessage('تم نسخ الكود بنجاح!');
      setTimeout(() => setShowSuccessMessage(null), 2000);
    } catch (error) {
      console.error('Error copying to clipboard:', error);
      alert('حدث خطأ أثناء نسخ الكود');
    }
  };

  const deleteAccessCode = async (codeId: string) => {
    if (window.confirm('هل أنت متأكد من حذف هذا الكود؟')) {
      try {
        setAccessCodes(accessCodes.filter(code => code.id !== codeId));
        setShowSuccessMessage('تم حذف الكود بنجاح!');
        setTimeout(() => setShowSuccessMessage(null), 3000);
      } catch (error) {
        console.error('Error deleting access code:', error);
        alert('حدث خطأ أثناء حذف الكود');
      }
    }
  };

  const getCourseTitles = (courseIds: string[]) => {
    return courseIds.map(id => {
      const course = courses.find(c => c.id === id);
      return course?.title || 'دورة غير معروفة';
    }).join(', ');
  };

  const isExpired = (expiresAt?: Date) => {
    return expiresAt && expiresAt < new Date();
  };

  if (loading) {
    return <LoadingSpinner text="جاري تحميل أكواد الوصول..." />;
  }

  return (
    <div className="space-y-6">
      {/* Success Message */}
      {showSuccessMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg relative">
          <div className="flex items-center">
            <i className="fas fa-check-circle ml-2"></i>
            <span>{showSuccessMessage}</span>
          </div>
          <button
            onClick={() => setShowSuccessMessage(null)}
            className="absolute top-0 bottom-0 left-0 px-4 py-3"
          >
            <i className="fas fa-times"></i>
          </button>
        </div>
      )}

      {/* Header */}
      <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
              إدارة أكواد الوصول
            </h1>
            <p className="text-gray-600 mt-2">إنشاء وإدارة أكواد وصول الطلاب للدورات</p>
          </div>
          <button
            onClick={() => setShowCreateModal(true)}
            className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg"
          >
            <i className="fas fa-plus ml-2"></i>
            إنشاء أكواد جديدة
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجمالي الأكواد</p>
              <p className="text-2xl font-bold text-gray-900">{accessCodes.length}</p>
            </div>
            <i className="fas fa-key text-blue-500 text-xl"></i>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">الأكواد المستخدمة</p>
              <p className="text-2xl font-bold text-gray-900">
                {accessCodes.filter(c => c.isUsed).length}
              </p>
            </div>
            <i className="fas fa-check-circle text-green-500 text-xl"></i>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">الأكواد المتاحة</p>
              <p className="text-2xl font-bold text-gray-900">
                {accessCodes.filter(c => !c.isUsed && !isExpired(c.expiresAt)).length}
              </p>
            </div>
            <i className="fas fa-clock text-yellow-500 text-xl"></i>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">الأكواد المنتهية</p>
              <p className="text-2xl font-bold text-gray-900">
                {accessCodes.filter(c => isExpired(c.expiresAt)).length}
              </p>
            </div>
            <i className="fas fa-exclamation-triangle text-red-500 text-xl"></i>
          </div>
        </div>
      </div>

      {/* Access Codes List */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">قائمة أكواد الوصول</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الكود
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الدورات
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  تاريخ الانتهاء
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  تاريخ الإنشاء
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {accessCodes.map((accessCode) => (
                <tr key={accessCode.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div className="flex items-center gap-2">
                      <span className="font-mono text-lg font-bold text-primary-blue">
                        {accessCode.code}
                      </span>
                      <button
                        onClick={() => copyToClipboard(accessCode.code)}
                        className="text-gray-400 hover:text-gray-600"
                        title="نسخ الكود"
                      >
                        <i className="fas fa-copy"></i>
                      </button>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {getCourseTitles(accessCode.courseIds)}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      accessCode.isUsed 
                        ? 'bg-green-100 text-green-800'
                        : isExpired(accessCode.expiresAt)
                        ? 'bg-red-100 text-red-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {accessCode.isUsed 
                        ? 'مستخدم' 
                        : isExpired(accessCode.expiresAt)
                        ? 'منتهي الصلاحية'
                        : 'متاح'}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">
                    {accessCode.expiresAt 
                      ? accessCode.expiresAt.toLocaleDateString('ar-EG')
                      : 'بلا انتهاء'}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">
                    {accessCode.createdAt.toLocaleDateString('ar-EG')}
                  </td>
                  <td className="px-6 py-4 text-sm font-medium">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => copyToClipboard(accessCode.code)}
                        className="text-blue-600 hover:text-blue-900"
                        title="نسخ الكود"
                      >
                        <i className="fas fa-copy"></i>
                      </button>
                      {!accessCode.isUsed && (
                        <button
                          onClick={() => deleteAccessCode(accessCode.id)}
                          className="text-red-600 hover:text-red-900 p-2 rounded-lg hover:bg-red-50 transition-colors"
                          title="حذف الكود"
                        >
                          <i className="fas fa-trash"></i>
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Create Access Codes Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              إنشاء أكواد وصول جديدة
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="form-label">الدورات المتاحة</label>
                <div className="space-y-2">
                  {courses.map(course => (
                    <label key={course.id} className="flex items-center">
                      <input
                        type="checkbox"
                        checked={newCodeData.courseIds.includes(course.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setNewCodeData({
                              ...newCodeData,
                              courseIds: [...newCodeData.courseIds, course.id]
                            });
                          } else {
                            setNewCodeData({
                              ...newCodeData,
                              courseIds: newCodeData.courseIds.filter(id => id !== course.id)
                            });
                          }
                        }}
                        className="ml-2"
                      />
                      <span className="text-sm">{course.title}</span>
                    </label>
                  ))}
                </div>
              </div>
              
              <div>
                <label className="form-label">عدد الأكواد</label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  value={newCodeData.quantity}
                  onChange={(e) => setNewCodeData({...newCodeData, quantity: parseInt(e.target.value) || 1})}
                  className="form-input"
                />
              </div>
              
              <div>
                <label className="form-label">تاريخ الانتهاء (اختياري)</label>
                <input
                  type="date"
                  value={newCodeData.expiresAt}
                  onChange={(e) => setNewCodeData({...newCodeData, expiresAt: e.target.value})}
                  className="form-input"
                />
              </div>
            </div>
            
            <div className="flex items-center gap-3 mt-6">
              <button
                onClick={generateAccessCodes}
                className="btn btn-primary"
                disabled={newCodeData.courseIds.length === 0}
              >
                إنشاء الأكواد
              </button>
              <button
                onClick={() => setShowCreateModal(false)}
                className="btn btn-outline"
              >
                إلغاء
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AccessCodeManagement;
