<!DOCTYPE html>
<html lang="ar" dir="rtl">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>منصة علاء عبد الحميد التعليمية</title>
    <meta name="description" content="منصة تعليمية احترافية لدورات التسويق متعددة المستويات" />
    <meta name="keywords" content="تعليم, دورات, تسويق, علاء عبد الحميد" />
    
    <!-- RTL Support -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      
      body {
        font-family: 'Cairo', sans-serif;
        direction: rtl;
        text-align: right;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }
      
      #root {
        min-height: 100vh;
      }
      
      .loading {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        color: white;
        font-size: 1.5rem;
      }
    </style>
    <script type="module" crossorigin src="/assets/index-CvjDiDZb.js"></script>
    <link rel="stylesheet" crossorigin href="/assets/index-C7Yvlbtp.css">
  </head>
  <body>
    <div id="root">
      <div class="loading">
        <i class="fas fa-spinner fa-spin" style="margin-left: 10px; color: #ffd700;"></i>
        جاري تحميل المنصة...
      </div>
    </div>
  </body>
</html>
