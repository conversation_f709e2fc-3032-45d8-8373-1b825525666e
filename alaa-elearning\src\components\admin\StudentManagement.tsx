import React, { useState, useEffect } from 'react';
import { User } from '../../types';
import LoadingSpinner from '../LoadingSpinner';

const StudentManagement: React.FC = () => {
  const [students, setStudents] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all');
  const [showSuccessMessage, setShowSuccessMessage] = useState<string | null>(null);

  useEffect(() => {
    loadStudents();
  }, []);

  const loadStudents = async () => {
    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockStudents: User[] = [
        {
          id: '1',
          name: 'أحمد محمد علي',
          email: '<EMAIL>',
          accessCode: '1234567',
          isActive: true,
          enrolledCourses: ['1', '2'],
          certificates: [],
          createdAt: new Date('2024-01-15'),
          updatedAt: new Date()
        },
        {
          id: '2',
          name: 'فاطمة حسن',
          email: '<EMAIL>',
          accessCode: '2345678',
          isActive: true,
          enrolledCourses: ['1'],
          certificates: [],
          createdAt: new Date('2024-02-10'),
          updatedAt: new Date()
        },
        {
          id: '3',
          name: 'محمد أحمد',
          email: '<EMAIL>',
          accessCode: '3456789',
          isActive: false,
          enrolledCourses: ['1', '2', '3'],
          certificates: [],
          createdAt: new Date('2024-01-20'),
          updatedAt: new Date()
        }
      ];
      
      setStudents(mockStudents);
    } catch (error) {
      console.error('Error loading students:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleStudentStatus = async (studentId: string) => {
    try {
      const student = students.find(s => s.id === studentId);
      if (!student) return;

      setStudents(students.map(student =>
        student.id === studentId
          ? { ...student, isActive: !student.isActive }
          : student
      ));

      setShowSuccessMessage(
        `تم ${student.isActive ? 'إيقاف' : 'تفعيل'} حساب ${student.name} بنجاح!`
      );
      setTimeout(() => setShowSuccessMessage(null), 3000);
    } catch (error) {
      console.error('Error toggling student status:', error);
      alert('حدث خطأ أثناء تحديث حالة الطالب');
    }
  };

  const deleteStudent = async (studentId: string) => {
    const student = students.find(s => s.id === studentId);
    if (!student) return;

    if (window.confirm(`هل أنت متأكد من حذف حساب ${student.name}؟`)) {
      try {
        setStudents(students.filter(s => s.id !== studentId));
        setShowSuccessMessage(`تم حذف حساب ${student.name} بنجاح!`);
        setTimeout(() => setShowSuccessMessage(null), 3000);
      } catch (error) {
        console.error('Error deleting student:', error);
        alert('حدث خطأ أثناء حذف الطالب');
      }
    }
  };

  const viewStudentDetails = (studentId: string) => {
    const student = students.find(s => s.id === studentId);
    if (student) {
      setShowSuccessMessage(`عرض تفاصيل ${student.name} - سيتم إضافة هذه الوظيفة قريباً`);
      setTimeout(() => setShowSuccessMessage(null), 3000);
    }
  };

  const filteredStudents = students.filter(student => {
    const matchesSearch = student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.accessCode.includes(searchTerm);
    
    const matchesFilter = filterStatus === 'all' || 
                         (filterStatus === 'active' && student.isActive) ||
                         (filterStatus === 'inactive' && !student.isActive);
    
    return matchesSearch && matchesFilter;
  });

  if (loading) {
    return <LoadingSpinner text="جاري تحميل الطلاب..." />;
  }

  return (
    <div className="space-y-6">
      {/* Success Message */}
      {showSuccessMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg relative">
          <div className="flex items-center">
            <i className="fas fa-check-circle ml-2"></i>
            <span>{showSuccessMessage}</span>
          </div>
          <button
            onClick={() => setShowSuccessMessage(null)}
            className="absolute top-0 bottom-0 left-0 px-4 py-3"
          >
            <i className="fas fa-times"></i>
          </button>
        </div>
      )}

      {/* Header */}
      <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
              إدارة الطلاب
            </h1>
            <p className="text-gray-600 mt-2">إدارة حسابات الطلاب وصلاحياتهم ومتابعة تقدمهم</p>
          </div>
          <div className="flex items-center gap-3">
            <button className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-200">
              <i className="fas fa-download ml-2"></i>
              تصدير البيانات
            </button>
            <button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-2 px-4 rounded-lg transition-all duration-200">
              <i className="fas fa-user-plus ml-2"></i>
              إضافة طالب
            </button>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl shadow-lg p-6 border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-blue-700">إجمالي الطلاب</p>
              <p className="text-3xl font-bold text-blue-900">{students.length}</p>
              <p className="text-xs text-blue-600 mt-1">جميع المسجلين</p>
            </div>
            <div className="w-14 h-14 bg-blue-500 rounded-xl flex items-center justify-center">
              <i className="fas fa-users text-white text-xl"></i>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl shadow-lg p-6 border border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-green-700">الطلاب النشطون</p>
              <p className="text-3xl font-bold text-green-900">
                {students.filter(s => s.isActive).length}
              </p>
              <p className="text-xs text-green-600 mt-1">حسابات مفعلة</p>
            </div>
            <div className="w-14 h-14 bg-green-500 rounded-xl flex items-center justify-center">
              <i className="fas fa-user-check text-white text-xl"></i>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl shadow-lg p-6 border border-purple-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-purple-700">طلاب جدد هذا الشهر</p>
              <p className="text-3xl font-bold text-purple-900">12</p>
              <p className="text-xs text-purple-600 mt-1">تسجيلات جديدة</p>
            </div>
            <div className="w-14 h-14 bg-purple-500 rounded-xl flex items-center justify-center">
              <i className="fas fa-user-plus text-white text-xl"></i>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl shadow-lg p-6 border border-yellow-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-yellow-700">معدل الإكمال</p>
              <p className="text-3xl font-bold text-yellow-900">78%</p>
              <p className="text-xs text-yellow-600 mt-1">نسبة إنهاء الدورات</p>
            </div>
            <div className="w-14 h-14 bg-yellow-500 rounded-xl flex items-center justify-center">
              <i className="fas fa-chart-line text-white text-xl"></i>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <i className="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
              <input
                type="text"
                placeholder="البحث بالاسم، البريد الإلكتروني، أو كود الوصول..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="form-input pr-10"
              />
            </div>
          </div>
          <div>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as 'all' | 'active' | 'inactive')}
              className="form-input"
            >
              <option value="all">جميع الطلاب</option>
              <option value="active">الطلاب النشطون</option>
              <option value="inactive">الطلاب غير النشطين</option>
            </select>
          </div>
        </div>
      </div>

      {/* Students List */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            قائمة الطلاب ({filteredStudents.length})
          </h3>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الطالب
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  كود الوصول
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الدورات المسجلة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الحالة
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  تاريخ التسجيل
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  الإجراءات
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredStudents.map((student) => (
                <tr key={student.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-primary-blue rounded-full flex items-center justify-center ml-3">
                        <span className="text-white font-medium">
                          {student.name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {student.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {student.email || 'لا يوجد بريد إلكتروني'}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className="font-mono text-sm bg-gray-100 px-2 py-1 rounded">
                      {student.accessCode}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <span className="text-sm text-gray-900">
                      {student.enrolledCourses.length} دورة
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      student.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {student.isActive ? 'نشط' : 'غير نشط'}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-500">
                    {student.createdAt.toLocaleDateString('ar-EG')}
                  </td>
                  <td className="px-6 py-4 text-sm font-medium">
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => viewStudentDetails(student.id)}
                        className="text-blue-600 hover:text-blue-900 p-2 rounded-lg hover:bg-blue-50 transition-colors"
                        title="عرض التفاصيل"
                      >
                        <i className="fas fa-eye"></i>
                      </button>
                      <button
                        onClick={() => {
                          // TODO: Implement edit functionality
                          setShowSuccessMessage('سيتم إضافة وظيفة التحرير قريباً');
                          setTimeout(() => setShowSuccessMessage(null), 3000);
                        }}
                        className="text-green-600 hover:text-green-900 p-2 rounded-lg hover:bg-green-50 transition-colors"
                        title="تحرير"
                      >
                        <i className="fas fa-edit"></i>
                      </button>
                      <button
                        onClick={() => toggleStudentStatus(student.id)}
                        className={`p-2 rounded-lg transition-colors ${
                          student.isActive
                            ? 'text-red-600 hover:text-red-900 hover:bg-red-50'
                            : 'text-green-600 hover:text-green-900 hover:bg-green-50'
                        }`}
                        title={student.isActive ? 'إيقاف الحساب' : 'تفعيل الحساب'}
                      >
                        <i className={`fas ${student.isActive ? 'fa-user-slash' : 'fa-user-check'}`}></i>
                      </button>
                      <button
                        onClick={() => deleteStudent(student.id)}
                        className="text-red-600 hover:text-red-900 p-2 rounded-lg hover:bg-red-50 transition-colors"
                        title="حذف الحساب"
                      >
                        <i className="fas fa-trash"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {filteredStudents.length === 0 && (
          <div className="text-center py-8">
            <i className="fas fa-users text-gray-400 text-4xl mb-4"></i>
            <p className="text-gray-500">لا توجد نتائج مطابقة للبحث</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default StudentManagement;
