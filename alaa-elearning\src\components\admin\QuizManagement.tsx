import React, { useState } from 'react';

const QuizManagement: React.FC = () => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState<string | null>(null);

  const handleCreateQuiz = () => {
    setShowCreateModal(true);
  };

  const handleQuizCreated = () => {
    setShowCreateModal(false);
    setShowSuccessMessage('تم إنشاء الاختبار بنجاح!');
    setTimeout(() => setShowSuccessMessage(null), 3000);
  };

  return (
    <div className="space-y-6">
      {/* Success Message */}
      {showSuccessMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded-lg relative">
          <div className="flex items-center">
            <i className="fas fa-check-circle ml-2"></i>
            <span>{showSuccessMessage}</span>
          </div>
          <button
            onClick={() => setShowSuccessMessage(null)}
            className="absolute top-0 bottom-0 left-0 px-4 py-3"
          >
            <i className="fas fa-times"></i>
          </button>
        </div>
      )}

      {/* Header */}
      <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">
              إدارة الاختبارات
            </h1>
            <p className="text-gray-600 mt-2">إنشاء وإدارة اختبارات الدورات التدريبية</p>
          </div>
          <button
            onClick={handleCreateQuiz}
            className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-200 transform hover:scale-105 shadow-lg"
          >
            <i className="fas fa-plus ml-2"></i>
            إنشاء اختبار جديد
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl shadow-lg p-6 border border-blue-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-blue-700">إجمالي الاختبارات</p>
              <p className="text-3xl font-bold text-blue-900">8</p>
              <p className="text-xs text-blue-600 mt-1">جميع الاختبارات</p>
            </div>
            <div className="w-14 h-14 bg-blue-500 rounded-xl flex items-center justify-center">
              <i className="fas fa-clipboard-question text-white text-xl"></i>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-xl shadow-lg p-6 border border-green-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-green-700">الاختبارات النشطة</p>
              <p className="text-3xl font-bold text-green-900">6</p>
              <p className="text-xs text-green-600 mt-1">متاحة للطلاب</p>
            </div>
            <div className="w-14 h-14 bg-green-500 rounded-xl flex items-center justify-center">
              <i className="fas fa-check-circle text-white text-xl"></i>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-xl shadow-lg p-6 border border-yellow-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-yellow-700">الاختبارات المكتملة</p>
              <p className="text-3xl font-bold text-yellow-900">234</p>
              <p className="text-xs text-yellow-600 mt-1">إجابات الطلاب</p>
            </div>
            <div className="w-14 h-14 bg-yellow-500 rounded-xl flex items-center justify-center">
              <i className="fas fa-chart-bar text-white text-xl"></i>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl shadow-lg p-6 border border-purple-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-semibold text-purple-700">متوسط الدرجات</p>
              <p className="text-3xl font-bold text-purple-900">85%</p>
              <p className="text-xs text-purple-600 mt-1">نسبة النجاح</p>
            </div>
            <div className="w-14 h-14 bg-purple-500 rounded-xl flex items-center justify-center">
              <i className="fas fa-trophy text-white text-xl"></i>
            </div>
          </div>
        </div>
      </div>

      {/* Quizzes List */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">قائمة الاختبارات</h3>
        </div>
        <div className="p-6">
          <div className="text-center py-12">
            <div className="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="fas fa-clipboard-question text-gray-400 text-2xl"></i>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">لا توجد اختبارات حالياً</h3>
            <p className="text-gray-600 mb-6">ابدأ بإنشاء اختبار جديد لطلابك</p>
            <button
              onClick={handleCreateQuiz}
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
            >
              <i className="fas fa-plus ml-2"></i>
              إنشاء اختبار جديد
            </button>
          </div>
        </div>
      </div>

      {/* Create Quiz Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-lg">
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6 rounded-t-2xl">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-xl font-bold">إنشاء اختبار جديد</h3>
                  <p className="text-blue-100 text-sm mt-1">إنشاء اختبار تفاعلي للطلاب</p>
                </div>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center hover:bg-opacity-30 transition-all duration-200"
                >
                  <i className="fas fa-times text-white"></i>
                </button>
              </div>
            </div>
            <div className="p-6 text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className="fas fa-clipboard-question text-blue-600 text-xl"></i>
              </div>
              <h4 className="text-lg font-semibold text-gray-900 mb-2">قريباً...</h4>
              <p className="text-gray-600 mb-6">سيتم إضافة منشئ الاختبارات التفاعلي قريباً</p>
              <button
                onClick={handleQuizCreated}
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-2 px-4 rounded-lg transition-colors"
              >
                موافق
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="text-center">
            <i className="fas fa-clipboard-question text-4xl text-blue-500 mb-4"></i>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">بنك الأسئلة</h3>
            <p className="text-gray-600 mb-4">إدارة مجموعة الأسئلة المتاحة</p>
            <button className="btn btn-outline">إدارة الأسئلة</button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="text-center">
            <i className="fas fa-tasks text-4xl text-green-500 mb-4"></i>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">الاختبارات النشطة</h3>
            <p className="text-gray-600 mb-4">عرض وإدارة الاختبارات الحالية</p>
            <button className="btn btn-outline">عرض الاختبارات</button>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="text-center">
            <i className="fas fa-chart-bar text-4xl text-purple-500 mb-4"></i>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">تقارير النتائج</h3>
            <p className="text-gray-600 mb-4">تحليل أداء الطلاب في الاختبارات</p>
            <button className="btn btn-outline">عرض التقارير</button>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md border border-gray-200 p-8 text-center">
        <i className="fas fa-tools text-4xl text-gray-400 mb-4"></i>
        <h3 className="text-lg font-semibold text-gray-700 mb-2">قيد التطوير</h3>
        <p className="text-gray-500">نظام إدارة الاختبارات سيكون متاحاً قريباً</p>
      </div>
    </div>
  );
};

export default QuizManagement;
