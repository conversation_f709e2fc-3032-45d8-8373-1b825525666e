import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import LoadingSpinner from '../components/LoadingSpinner';
import '../styles/admin.css';

// Admin Components
import AdminSidebar from '../components/admin/AdminSidebar';
import DashboardOverview from '../components/admin/DashboardOverview';
import CourseManagement from '../components/admin/CourseManagement';
import StudentManagement from '../components/admin/StudentManagement';
import AccessCodeManagement from '../components/admin/AccessCodeManagement';
import CertificateManagement from '../components/admin/CertificateManagement';
import QuizManagement from '../components/admin/QuizManagement';
import SettingsManagement from '../components/admin/SettingsManagement';

type AdminView = 'overview' | 'courses' | 'students' | 'access-codes' | 'certificates' | 'quizzes' | 'settings';

const AdminDashboard: React.FC = () => {
  const { admin, loading } = useAuth();
  const [currentView, setCurrentView] = useState<AdminView>('overview');
  const [sidebarOpen, setSidebarOpen] = useState(true);

  useEffect(() => {
    // Set page title
    document.title = 'لوحة الإدارة - منصة علاء عبد الحميد التعليمية';
  }, []);

  if (loading) {
    return <LoadingSpinner text="جاري تحميل لوحة الإدارة..." />;
  }

  if (!admin) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-red-50">
        <div className="text-center">
          <i className="fas fa-exclamation-triangle text-red-500 text-4xl mb-4"></i>
          <h2 className="text-2xl font-bold text-red-700 mb-2">غير مخول للوصول</h2>
          <p className="text-red-600">ليس لديك صلاحية للوصول لهذه الصفحة</p>
        </div>
      </div>
    );
  }

  const renderCurrentView = () => {
    switch (currentView) {
      case 'overview':
        return <DashboardOverview onNavigateToView={setCurrentView} />;
      case 'courses':
        return <CourseManagement />;
      case 'students':
        return <StudentManagement />;
      case 'access-codes':
        return <AccessCodeManagement />;
      case 'certificates':
        return <CertificateManagement />;
      case 'quizzes':
        return <QuizManagement />;
      case 'settings':
        return <SettingsManagement />;
      default:
        return <DashboardOverview onNavigateToView={setCurrentView} />;
    }
  };

  return (
    <div className="admin-dashboard flex">
      {/* Sidebar */}
      <AdminSidebar
        currentView={currentView}
        setCurrentView={setCurrentView}
        isOpen={sidebarOpen}
        setIsOpen={setSidebarOpen}
        admin={admin}
      />

      {/* Main Content */}
      <div className={`flex-1 transition-all duration-300 ${sidebarOpen ? 'mr-64' : 'mr-16'} min-h-screen`}>
        {/* Top Bar */}
        <header className="admin-header sticky top-0 z-40">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="p-3 rounded-xl hover:bg-blue-50 transition-all duration-200 text-blue-600 hover:text-blue-700"
              >
                <i className="fas fa-bars text-lg"></i>
              </button>
              <div>
                <h1 className="admin-header-title">
                  لوحة الإدارة
                </h1>
                <p className="text-sm text-gray-500">منصة علاء عبد الحميد التعليمية</p>
              </div>
            </div>

            <div className="flex items-center gap-4">
              {/* Notifications */}
              <button
                onClick={() => {
                  // TODO: Show notifications modal
                  console.log('عرض الإشعارات');
                }}
                className="relative p-3 rounded-xl hover:bg-blue-50 transition-all duration-200 text-gray-600 hover:text-blue-600"
              >
                <i className="fas fa-bell text-lg"></i>
                <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                  3
                </span>
              </button>

              {/* Settings */}
              <button
                onClick={() => setCurrentView('settings')}
                className="p-3 rounded-xl hover:bg-blue-50 transition-all duration-200 text-gray-600 hover:text-blue-600"
              >
                <i className="fas fa-cog text-lg"></i>
              </button>

              {/* Admin Profile */}
              <div className="admin-header-profile">
                <div className="text-right">
                  <p className="text-sm font-semibold">
                    {admin.name}
                  </p>
                  <p className="text-xs text-blue-100">
                    {admin.role === 'super_admin' ? 'مدير عام' : 'مدير'}
                  </p>
                </div>
                <div className="admin-header-avatar">
                  <i className="fas fa-user-shield text-white"></i>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content Area */}
        <main className="p-6 bg-gray-50 min-h-[calc(100vh-80px)]">
          <div className="max-w-7xl mx-auto">
            {renderCurrentView()}
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminDashboard;
